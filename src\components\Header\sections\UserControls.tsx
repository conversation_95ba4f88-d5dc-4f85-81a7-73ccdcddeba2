import { PersonIcon } from "@radix-ui/react-icons"
import React from "react"
import { useUserActions } from "../hooks"
import type { UserControlsProps } from "../type/types"

export const UserControls: React.FC<UserControlsProps> = ({
  onShare,
  onUserClick,
}) => {
  const { handleShareClick, handleUserClick } = useUserActions()

  return (
    <div className="user-controls">
      <button
        className="share-btn"
        onClick={onShare || handleShareClick}
        aria-label="分享"
      >
        分享
      </button>
      <button
        className="user-avatar"
        onClick={onUserClick || handleUserClick}
        aria-label="用户"
      >
        <PersonIcon />
      </button>
    </div>
  )
}
