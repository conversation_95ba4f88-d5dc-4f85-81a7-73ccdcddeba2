import React, { useState } from "react"
import { GenericToolbar<PERSON>enderer, GenericToolbarItem } from "../utils/GenericToolbarRenderer"
import { 
  PlusIcon, 
  FontBoldIcon, 
  FontItalicIcon,
  TextAlignLeftIcon,
  TextAlignCenterIcon,
  TextAlignRightIcon 
} from "../icons"

// 示例1：简单的编辑工具栏
export const SimpleEditToolbar: React.FC = () => {
  const [isBold, setIsBold] = useState(false)
  const [isItalic, setIsItalic] = useState(false)
  const [hasSelection, setHasSelection] = useState(true)

  const context = {
    isBold,
    isItalic,
    hasSelection,
    onToggleBold: () => setIsBold(!isBold),
    onToggleItalic: () => setIsItalic(!isItalic),
    onAddItem: () => console.log('添加项目'),
  }

  const config: GenericToolbarItem[] = [
    {
      type: "button",
      id: "add",
      label: "添加",
      icon: PlusIcon,
      onClick: "onAddItem",
    },
    { type: "separator" },
    {
      type: "button",
      id: "bold",
      label: "粗体",
      icon: FontBoldIcon,
      onClick: "onToggleBold",
      active: "isBold",
      disabled: "!hasSelection",
    },
    {
      type: "button",
      id: "italic", 
      label: "斜体",
      icon: FontItalicIcon,
      onClick: "onToggleItalic",
      active: "isItalic",
      disabled: "!hasSelection",
    },
  ]

  return (
    <div className="p-4">
      <h3>简单编辑工具栏</h3>
      <GenericToolbarRenderer
        config={config}
        context={context}
        ariaLabel="简单编辑工具栏"
        className="border rounded p-2"
      />
      <div className="mt-4">
        <label>
          <input 
            type="checkbox" 
            checked={hasSelection}
            onChange={(e) => setHasSelection(e.target.checked)}
          />
          有文本选择
        </label>
      </div>
    </div>
  )
}

// 示例2：复杂的格式化工具栏
export const FormattingToolbar: React.FC = () => {
  const [fontSize, setFontSize] = useState('14')
  const [fontFamily, setFontFamily] = useState('Arial')
  const [textAlign, setTextAlign] = useState('left')
  const [textColor, setTextColor] = useState('#000000')
  const [isEnabled, setIsEnabled] = useState(true)

  const context = {
    fontSize,
    fontFamily,
    textAlign,
    textColor,
    isEnabled,
    onFontSizeChange: setFontSize,
    onFontFamilyChange: setFontFamily,
    onTextAlignChange: setTextAlign,
    onTextColorChange: setTextColor,
  }

  const config: GenericToolbarItem[] = [
    {
      type: "select",
      id: "fontFamily",
      label: "字体",
      options: ["Arial", "Helvetica", "Times New Roman", "Courier New"],
      value: "fontFamily",
      onChange: "onFontFamilyChange",
      disabled: "!isEnabled",
    },
    {
      type: "select",
      id: "fontSize",
      label: "字号",
      options: ["12", "14", "16", "18", "20", "24"],
      value: "fontSize",
      onChange: "onFontSizeChange",
      disabled: "!isEnabled",
    },
    { type: "separator" },
    {
      type: "group",
      id: "textAlign",
      className: "flex",
      children: [
        {
          type: "button",
          id: "alignLeft",
          label: "左对齐",
          icon: TextAlignLeftIcon,
          onClick: () => setTextAlign('left'),
          active: "textAlign === 'left'",
          disabled: "!isEnabled",
        },
        {
          type: "button",
          id: "alignCenter",
          label: "居中对齐",
          icon: TextAlignCenterIcon,
          onClick: () => setTextAlign('center'),
          active: "textAlign === 'center'",
          disabled: "!isEnabled",
        },
        {
          type: "button",
          id: "alignRight",
          label: "右对齐",
          icon: TextAlignRightIcon,
          onClick: () => setTextAlign('right'),
          active: "textAlign === 'right'",
          disabled: "!isEnabled",
        },
      ]
    },
    { type: "separator" },
    {
      type: "color",
      id: "textColor",
      label: "文字颜色",
      value: "textColor",
      onChange: "onTextColorChange",
      disabled: "!isEnabled",
    },
  ]

  return (
    <div className="p-4">
      <h3>格式化工具栏</h3>
      <GenericToolbarRenderer
        config={config}
        context={context}
        ariaLabel="格式化工具栏"
        className="border rounded p-2"
        separatorClassName="mx-2 border-l border-gray-300"
      />
      <div className="mt-4 space-y-2">
        <label>
          <input 
            type="checkbox" 
            checked={isEnabled}
            onChange={(e) => setIsEnabled(e.target.checked)}
          />
          启用工具栏
        </label>
        <div>
          <strong>当前设置：</strong>
          <ul className="list-disc list-inside">
            <li>字体：{fontFamily}</li>
            <li>字号：{fontSize}</li>
            <li>对齐：{textAlign}</li>
            <li>颜色：{textColor}</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

// 示例3：自定义组件工具栏
const CustomSlider: React.FC<{
  value: number
  onChange: (value: number) => void
  min?: number
  max?: number
  step?: number
  label?: string
}> = ({ value, onChange, min = 0, max = 100, step = 1, label }) => {
  return (
    <div className="flex items-center space-x-2">
      {label && <span className="text-sm">{label}:</span>}
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange(Number(e.target.value))}
        className="w-20"
      />
      <span className="text-sm w-8">{value}</span>
    </div>
  )
}

export const CustomComponentToolbar: React.FC = () => {
  const [opacity, setOpacity] = useState(100)
  const [rotation, setRotation] = useState(0)

  const context = {
    opacity,
    rotation,
    onOpacityChange: setOpacity,
    onRotationChange: setRotation,
  }

  const config: GenericToolbarItem[] = [
    {
      type: "custom",
      id: "opacity",
      customComponent: CustomSlider,
      customProps: {
        label: "透明度",
        min: 0,
        max: 100,
        step: 5,
      },
      value: "opacity",
      onChange: "onOpacityChange",
    },
    { type: "separator" },
    {
      type: "custom",
      id: "rotation",
      customComponent: CustomSlider,
      customProps: {
        label: "旋转",
        min: -180,
        max: 180,
        step: 15,
      },
      value: "rotation",
      onChange: "onRotationChange",
    },
  ]

  return (
    <div className="p-4">
      <h3>自定义组件工具栏</h3>
      <GenericToolbarRenderer
        config={config}
        context={context}
        ariaLabel="自定义组件工具栏"
        className="border rounded p-2"
      />
      <div className="mt-4">
        <div 
          className="w-20 h-20 bg-blue-500 transition-all duration-200"
          style={{
            opacity: opacity / 100,
            transform: `rotate(${rotation}deg)`
          }}
        />
      </div>
    </div>
  )
}

// 组合示例
export const ToolbarExamples: React.FC = () => {
  return (
    <div className="space-y-8">
      <SimpleEditToolbar />
      <FormattingToolbar />
      <CustomComponentToolbar />
    </div>
  )
}
