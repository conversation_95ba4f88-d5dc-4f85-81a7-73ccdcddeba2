/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect } from "react"
import { Header } from "../components/Header"
import { MindMapCanvas } from "../components/MindContent/tools/MindMapCanvas"
import Toolbar from "../components/Toolbar"
import { useContextMenu } from "../hooks/useContextMenu"
import { useMindMap } from "../hooks/useMindMap"
import { useNodeEditor } from "../hooks/useNodeEditor"
import { useUI } from "../hooks/useUI"
import { useViewDrag } from "../hooks/useViewDrag"

export const MindMapPage = () => {
  // 使用Redux状态管理的hooks
  const { activeTab, showAddButton, setActiveTab, setShowAddButton } = useUI()

  // 使用自定义 hooks
  const {
    mindMapNodes,
    selectedNodeId,
    setSelectedNodeId,
    addChildNode,
    addSiblingNode,
    addParentNode,
    deleteNode,
    deleteNodeWithChildren,
    updateNode,
    recalculateLayout,
    resetMindMap: _resetMindMap,
    // importMindMap, // 暂时注释掉，因为没有在UI中使用
  } = useMindMap()

  const {
    isEditingNode,
    editingText,
    setEditingText,
    showStylePanel,
    setShowStylePanel,
    editingStyle,
    startEditNode,
    saveNodeEdit,
    cancelNodeEdit,
    updateEditingStyle,
  } = useNodeEditor()

  const {
    isDragging,
    viewOffset,
    handleNodeMouseDown,
    handleMouseMove,
    handleMouseUp,
  } = useViewDrag()

  const { setContextMenuNodeId, hideContextMenu } = useContextMenu()

  // 监听节点数量变化，延迟重新计算布局和连接线
  useEffect(() => {
    const nodeCount = Object.keys(mindMapNodes).length
    if (nodeCount > 1) {
      // 避免初始化时触发
      const timer = setTimeout(() => {
        recalculateLayout()
        // 触发连接线重新渲染
        window.dispatchEvent(new CustomEvent("recalculateLayout"))
      }, 200)

      return () => clearTimeout(timer)
    }
  }, [Object.keys(mindMapNodes).length, recalculateLayout])

  // 键盘事件监听
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Delete" && selectedNodeId && selectedNodeId !== "root") {
        e.preventDefault()
        deleteNodeWithChildren(selectedNodeId)
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => {
      document.removeEventListener("keydown", handleKeyDown)
    }
  }, [selectedNodeId, deleteNodeWithChildren])

  // 全局点击监听器 - 点击任何位置（除了节点工具栏）都取消选中和编辑状态
  useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement

      // 检查是否点击了节点浮动工具栏
      const isFloatingToolbar = target.closest(".node-floating-toolbar")

      // 检查是否点击了header工具栏
      const isHeaderToolbar = target.closest(".toolbar")

      // 检查是否点击了header导航
      const isHeaderNav = target.closest(".header-nav")

      // 检查是否点击了节点本身
      const isNode = target.closest(".mindmap-node")

      // 检查是否点击了加号按钮
      const isAddButton = target.closest(".node-add-btn")

      // 检查是否点击了右键菜单
      const isContextMenu = target.closest(".context-menu")

      // 如果点击的不是以上任何元素，则取消选中和编辑状态
      if (
        !isFloatingToolbar &&
        !isHeaderToolbar &&
        !isHeaderNav &&
        !isNode &&
        !isAddButton &&
        !isContextMenu
      ) {
        if (selectedNodeId || isEditingNode) {
          setSelectedNodeId(null)
          if (isEditingNode) {
            cancelNodeEdit()
          }
          hideContextMenu()
          setShowStylePanel(null)
          setShowAddButton(null)
        }
      }
    }

    document.addEventListener("click", handleGlobalClick, false)
    return () => {
      document.removeEventListener("click", handleGlobalClick, false)
    }
  }, [
    selectedNodeId,
    isEditingNode,
    cancelNodeEdit,
    hideContextMenu,
    setSelectedNodeId,
    setShowAddButton,
    setShowStylePanel,
  ])

  // 单击选择节点（用于header工具栏）
  const selectNode = (nodeId: string) => {
    setSelectedNodeId(nodeId)

    // 隐藏样式面板（只有双击才显示）
    setShowStylePanel(null)

    // 如果点击的节点没有子节点，显示加号按钮
    const node = mindMapNodes[nodeId]
    if (node && node.children.length === 0) {
      setShowAddButton(nodeId)
    } else {
      setShowAddButton(null)
    }
  }

  // 开始编辑节点
  const handleStartEditNode = (nodeId: string) => {
    const node = mindMapNodes[nodeId]
    if (node) {
      startEditNode(nodeId, node.text, node.style)
    }
  }

  // 保存节点编辑
  const handleSaveNodeEdit = () => {
    saveNodeEdit((text, style) => {
      if (isEditingNode) {
        updateNode(isEditingNode, text, style)
        // 延迟重新计算布局，等待DOM更新
        setTimeout(() => {
          recalculateLayout()
        }, 100)
      }
    })
  }

  // 样式更改处理
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleStyleChange = (nodeId: string, style: any) => {
    updateEditingStyle(style, (newStyle) => {
      updateNode(nodeId, mindMapNodes[nodeId].text, newStyle)
      // 延迟重新计算布局，等待DOM更新
      setTimeout(() => {
        recalculateLayout()
        // 触发连接线重新渲染
        window.dispatchEvent(new CustomEvent("recalculateLayout"))
      }, 150)
    })
  }

  // 添加子节点处理
  const handleAddChildNode = () => {
    if (selectedNodeId) {
      addChildNode(selectedNodeId)
      // 新节点会自动被选中，所以我们可以使用selectedNodeId来设置showAddButton
      // 但由于Redux状态更新是异步的，我们需要在下一个渲染周期中处理
    }
  }

  // 导入思维导图处理
  // const handleImportMindMap = (event: React.ChangeEvent<HTMLInputElement>) => {
  //   const file = event.target.files?.[0]
  //   if (!file) return

  //   const reader = new FileReader()
  //   reader.onload = (e) => {
  //     try {
  //       const content = e.target?.result as string
  //       const importedNodes = JSON.parse(content)

  //       if (importMindMap(importedNodes)) {
  //         alert("导入成功！")
  //       } else {
  //         alert("文件格式不正确，请选择有效的思维导图文件")
  //       }
  //     } catch (error) {
  //       console.error("导入思维导图失败:", error)
  //       alert("导入失败，请检查文件格式")
  //     }
  //   }
  //   reader.readAsText(file)

  //   // 清空input值，允许重复选择同一文件
  //   event.target.value = ""
  // }

  // 画布点击处理 - 现在由全局点击监听器处理，这里保留为空函数以避免重复处理
  const handleCanvasClick = () => {
    // 全局点击监听器已经处理了取消选中和编辑状态的逻辑
    // 这里不需要重复处理，避免冲突
  }

  // AI生成处理
  const handleAIGenerate = async (nodeId: string, content: string) => {
    if (nodeId !== "root") {
      console.warn("AI功能仅支持根节点")
      return
    }

    try {
      console.log("开始AI生成，输入内容:", content)

      // 调用AI服务
      const { generateMindMapFromAI } = await import("../services/aiService")
      const response = await generateMindMapFromAI(content)

      if (response.success && response.data?.nodes) {
        console.log("AI生成成功:", response.data.nodes)

        // 将AI生成的节点添加到思维导图中
        response.data.nodes.forEach((aiNode, index) => {
          // 添加一级节点（分支主题）
          addChildNode(nodeId)

          // 由于addChildNode会自动选中新节点，我们可以通过selectedNodeId获取新节点ID
          setTimeout(() => {
            const currentSelectedId = selectedNodeId
            if (currentSelectedId && mindMapNodes[currentSelectedId]) {
              // 更新节点文本
              updateNode(currentSelectedId, aiNode.text)

              // 添加二级节点（子主题）
              if (aiNode.children && aiNode.children.length > 0) {
                aiNode.children.forEach((childNode, childIndex) => {
                  setTimeout(() => {
                    addChildNode(currentSelectedId)

                    // 再次获取新选中的节点ID来更新子节点文本
                    setTimeout(() => {
                      const childSelectedId = selectedNodeId
                      if (childSelectedId && mindMapNodes[childSelectedId]) {
                        updateNode(childSelectedId, childNode.text)
                      }
                    }, 50)
                  }, 200 * childIndex)
                })
              }
            }
          }, 300 * index)
        })

        // 重新计算布局
        setTimeout(() => {
          recalculateLayout()
          // 触发连接线重新渲染
          window.dispatchEvent(new CustomEvent("recalculateLayout"))
        }, 2000)

        console.log("AI生成的思维导图已添加")
      } else {
        console.error("AI生成失败:", response.error || response.message)
        alert(response.message || "AI生成失败，请稍后重试")
      }
    } catch (error) {
      console.error("AI生成过程中出错:", error)
      alert("AI功能暂时不可用，请稍后重试")
    }
  }

  // 切换加粗
  const handleToggleBold = () => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        fontWeight:
          currentStyle.fontWeight === "bold"
            ? "normal"
            : ("bold" as "normal" | "bold"),
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  // 切换斜体
  const handleToggleItalic = () => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        fontStyle:
          currentStyle.fontStyle === "italic"
            ? "normal"
            : ("italic" as "normal" | "italic"),
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  // 切换下划线
  const handleToggleUnderline = () => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        textDecoration:
          currentStyle.textDecoration === "underline"
            ? "none"
            : ("underline" as "none" | "underline"),
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  // 修改边框宽度
  const handleBorderWidthChange = (borderWidth: number) => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        borderWidth: borderWidth,
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  // 获取默认样式
  const getDefaultStyle = () => ({
    fontSize: 14,
    fontFamily: "微软雅黑",
    fontWeight: "normal" as const,
    fontStyle: "normal" as const,
    textDecoration: "none" as const,
    color: "#000000",
    backgroundColor: "#ffffff",
    borderColor: "#d1d5db",
    borderWidth: 1,
  })

  // 更改字体颜色
  const handleColorChange = (color: string) => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        color: color,
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  // 更改字体族
  const handleFontFamilyChange = (fontFamily: string) => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        fontFamily: fontFamily,
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  // 更改字体大小
  const handleFontSizeChange = (fontSize: number) => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        fontSize: fontSize,
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  // 更改文字对齐
  const handleTextAlignChange = (textAlign: "left" | "center" | "right") => {
    if (selectedNodeId) {
      const node = mindMapNodes[selectedNodeId]
      const currentStyle = node.style || getDefaultStyle()
      const newStyle = {
        ...currentStyle,
        textAlign: textAlign,
      }
      updateNode(selectedNodeId, node.text, newStyle)
    }
  }

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Top Header */}
      <Header activeTab={activeTab} onTabChange={setActiveTab} />

      {/* Toolbar */}
      <Toolbar
        activeTab={activeTab}
        selectedNodeId={selectedNodeId}
        isEditingNode={isEditingNode}
        selectedNodeStyle={
          selectedNodeId ? mindMapNodes[selectedNodeId]?.style : undefined
        }
        onAddChildNode={handleAddChildNode}
        onToggleBold={handleToggleBold}
        onToggleItalic={handleToggleItalic}
        onColorChange={handleColorChange}
        onFontFamilyChange={handleFontFamilyChange}
        onFontSizeChange={handleFontSizeChange}
        onTextAlignChange={handleTextAlignChange}
        onBorderWidthChange={handleBorderWidthChange}
      />

      {/* Main Content Area - Mind Map */}
      <MindMapCanvas
        mindMapNodes={mindMapNodes}
        selectedNodeId={selectedNodeId}
        isEditingNode={isEditingNode}
        editingText={editingText}
        editingStyle={editingStyle}
        showStylePanel={showStylePanel}
        showAddButton={showAddButton}
        isDragging={isDragging}
        viewOffset={viewOffset}
        onNodeSelect={selectNode}
        onNodeDoubleClick={handleStartEditNode} // 保留用于编辑功能
        onNodeContextMenu={(_e, nodeId) => {
          setContextMenuNodeId(nodeId)
        }}
        onNodeMouseDown={handleNodeMouseDown}
        onEditingTextChange={setEditingText}
        onSaveEdit={handleSaveNodeEdit}
        onCancelEdit={cancelNodeEdit}
        onStyleChange={handleStyleChange}
        onAddChildNode={(nodeId) => {
          addChildNode(nodeId)
          setShowAddButton(null) // 添加节点后隐藏加号
        }}
        onCanvasClick={handleCanvasClick}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onToggleBold={handleToggleBold}
        onToggleItalic={handleToggleItalic}
        onToggleUnderline={handleToggleUnderline}
        onColorChange={handleColorChange}
        onFontSizeChange={handleFontSizeChange}
        onTextAlignChange={handleTextAlignChange}
        onAddSiblingNode={addSiblingNode}
        onAddParentNode={addParentNode}
        onDeleteNode={deleteNode}
        onAIGenerate={handleAIGenerate}
      />
    </div>
  )
}
