import React from "react"
import { MoveIcon, ShareIcon, ShortcutIcon, StarIcon, TagIcon } from "./icons"

// 图标映射
const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
  share: ShareIcon,
  star: StarIcon,
  tag: TagIcon,
  move: MoveIcon,
  shortcut: ShortcutIcon,
}

// 获取图标组件
export const getIconComponent = (iconName: string) => {
  return iconMap[iconName] || (() => React.createElement("span", null, "?"))
} 