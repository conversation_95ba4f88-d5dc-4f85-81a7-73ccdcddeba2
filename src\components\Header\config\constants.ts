import {
  HamburgerMenuIcon,
  HomeIcon,
  PlusIcon,
  StarIcon,
  TrashIcon
} from "@radix-ui/react-icons"
import type { IconActionItem, TabItem } from "../type/types"

// 创建文件操作按钮配置的工厂函数
export const createFileActions = (handlers: {
  handleHome: () => void
  handleNew: () => void
  handleReset: () => void
  handleMenu: () => void
  toggleStar: () => void
}): IconActionItem[] => [
  {
    id: "home",
    icon: HomeIcon,
    label: "首页",
    onClick: handlers.handleHome,
    disabled: false,
  },
  {
    id: "add",
    icon: PlusIcon,
    label: "新建",
    onClick: handlers.handleNew,
    disabled: false,
  },
  {
    id: "reset",
    icon: TrashIcon,
    label: "重置",
    onClick: handlers.handleReset,
    disabled: false,
  },
  {
    id: "menu",
    icon: HamburgerMenuIcon,
    label: "文档",
    onClick: handlers.handleMenu,
    disabled: false,
  },
  {
    id: "star",
    icon: StarIcon,
    label: "星标",
    onClick: handlers.toggleStar,
    disabled: false,
  },
]

// 标签页配置
export const tabConfig: TabItem[] = [
  { id: "开始", label: "开始" },
  { id: "样式", label: "样式" },
  { id: "插入", label: "插入" },
  { id: "视图", label: "视图" },
  { id: "导出", label: "导出" },
]

// 创建文件操作配置的工厂函数
export const createFileOperations = (handlers: {
  handleShare: () => void
  toggleStar: () => void
  handleTag: () => void
}) => [
  {
    id: "share",
    label: "分享",
    icon: "share",
    onClick: handlers.handleShare,
  },
  {
    id: "star",
    label: "星标",
    icon: "star",
    onClick: handlers.toggleStar,
  },
  {
    id: "tag",
    label: "标签",
    icon: "tag",
    onClick: handlers.handleTag,
  },
]

// 创建位置操作配置的工厂函数
export const createLocationOperations = (handlers: {
  handleMove: () => void
  handleAddShortcut: () => void
}) => [
  {
    id: "move",
    label: "移动",
    icon: "move",
    onClick: handlers.handleMove,
  },
  {
    id: "shortcut",
    label: "添加快捷方式到",
    icon: "shortcut",
    onClick: handlers.handleAddShortcut,
  },
]