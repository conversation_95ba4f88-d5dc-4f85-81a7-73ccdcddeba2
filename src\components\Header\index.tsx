import React from "react"
import "./Header.css"
import { FileControls } from "./sections/FileControls"
import { TabNavigation } from "./sections/TabNavigation"
import { UserControls } from "./sections/UserControls"

interface HeaderProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

export const Header: React.FC<HeaderProps> = ({ activeTab, onTabChange }) => {
  return (
    <header className="header">
      <div className="header-container">
        <FileControls />
        <TabNavigation activeTab={activeTab} onTabChange={onTabChange} />
        <UserControls />
      </div>
    </header>
  )
}
