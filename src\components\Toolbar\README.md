# Toolbar 组件重构说明

## 重构概述

将原本重复冗长的工具栏组件重构为配置驱动的架构，大大减少了代码重复，提高了可维护性。

## 新的架构

### 1. 配置文件 (Config)

- `config/startToolbarConfig.ts` - 开始工具栏配置
- `config/styleToolbarConfig.ts` - 样式工具栏配置

### 2. 通用渲染器 (Renderer)

- `utils/ToolbarRenderer.tsx` - 通用工具栏渲染器，根据配置动态生成工具栏

### 3. 图标统一导出

- `icons/index.ts` - 统一导出所有图标组件

### 4. 重构后的组件

- `sections/StartToolbar.tsx` - 简化为配置驱动
- `sections/StyleToolbar.tsx` - 简化为配置驱动

## 配置项说明

### ToolbarItem 类型

```typescript
interface ToolbarItem {
  type: "button" | "select" | "color" | "separator" | "group"
  id?: string
  label?: string
  icon?: React.ComponentType
  text?: string
  className?: string
  onClick?: string // 对应 props 中的方法名
  active?: string // 对应 props 中的状态属性
  disabled?: boolean | string // 可以是布尔值或者条件表达式
  options?: string[] // 用于 select 类型
  value?: string // 对应 props 中的值属性
  onChange?: string // 对应 props 中的方法名
  children?: ToolbarItem[] // 用于 group 类型
}
```

### 支持的组件类型

1. **button** - 按钮组件
   - 支持图标、文本、点击事件、激活状态
2. **select** - 下拉选择组件
   - 支持选项列表、值绑定、变更事件
3. **color** - 颜色选择组件
   - 支持颜色值绑定、变更事件
4. **separator** - 分隔符
   - 支持自定义样式类名
5. **group** - 组合组件
   - 支持嵌套子组件

## 优势

### 1. 代码复用

- 消除了大量重复的 JSX 代码
- 统一的渲染逻辑

### 2. 配置驱动

- 通过配置文件定义工具栏结构
- 易于添加、删除、修改工具栏项目

### 3. 类型安全

- 完整的 TypeScript 类型定义
- 编译时错误检查

### 4. 易于维护

- 集中的配置管理
- 清晰的组件结构

## 使用示例

### 添加新的工具栏项目

```typescript
// 在配置文件中添加
{
  type: "button",
  id: "newFeature",
  label: "新功能",
  icon: NewFeatureIcon,
  onClick: "onNewFeature",
  disabled: "!isEnabled",
}
```

### 修改现有项目

只需要在配置文件中修改对应的配置项，无需修改组件代码。

## 迁移说明

原有的工具栏组件已经完全重构，但保持了相同的 API 接口，因此不会影响使用这些组件的父组件。

所有的功能和方法都得到了保留，只是实现方式从硬编码改为了配置驱动。

## 🌟 通用工具栏渲染器

### GenericToolbarRenderer - 完全通用的解决方案

基于重构经验，我们还创建了 `GenericToolbarRenderer`，这是一个完全通用的工具栏渲染器，可以用于任何项目：

#### ✨ 特性

- **完全配置驱动**：通过 JSON 配置定义任何工具栏
- **类型安全**：完整的 TypeScript 支持
- **高度可扩展**：支持自定义组件类型
- **表达式支持**：动态属性和条件逻辑
- **零依赖冲突**：可以在任何 React 项目中使用

#### 🚀 使用示例

```typescript
import { GenericToolbarRenderer } from "./utils/GenericToolbarRenderer"

const MyToolbar = () => {
  const context = {
    isEnabled: true,
    onSave: () => console.log("保存"),
    onDelete: () => console.log("删除"),
  }

  const config = [
    {
      type: "button",
      label: "保存",
      icon: SaveIcon,
      onClick: "onSave",
      disabled: "!isEnabled",
    },
    { type: "separator" },
    {
      type: "button",
      label: "删除",
      icon: DeleteIcon,
      onClick: "onDelete",
    },
  ]

  return (
    <GenericToolbarRenderer
      config={config}
      context={context}
      ariaLabel="我的工具栏"
    />
  )
}
```

#### 📁 相关文件

- `utils/GenericToolbarRenderer.tsx` - 通用渲染器核心
- `utils/GenericToolbarRenderer.md` - 详细文档和 API
- `examples/GenericToolbarExample.tsx` - 使用示例

#### 🎯 适用场景

- **任何需要工具栏的应用**：编辑器、管理后台、设计工具等
- **快速原型开发**：通过配置快速创建功能完整的工具栏
- **组件库开发**：作为基础组件提供给其他项目
- **动态界面**：根据用户权限或状态动态生成工具栏
