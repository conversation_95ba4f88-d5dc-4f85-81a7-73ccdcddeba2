import React from "react"

export const StyleBorderColorIcon: React.FC = () => (
  <svg
    className="icon"
    viewBox="0 0 1028 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="20260"
    width="32"
    height="32"
  >
    <path
      d="M320 768H0V0h834v64H64v640h256z"
      fill="#707070"
      p-id="20261"
    ></path>
    <path d="M0 832h1024v192H0z" p-id="20262" fill="#707070"></path>
    <path
      d="M1004.3 83.8c-26.4-26.4-68.9-26.4-95.2 0l-47.6 47.8 95.2 95.5 47.6-47.7c26.3-26.5 26.3-69.2 0-95.6zM812.4 178.8L526.8 465.2l95.2 95.5 285.6-286.4zM384 704l190.4-95.5-95.2-95.5z"
      fill="#707070"
      p-id="20263"
    ></path>
  </svg>
)
