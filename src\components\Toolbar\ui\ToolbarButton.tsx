import * as ToolbarPrimitive from "@radix-ui/react-toolbar"
import * as Tooltip from "@radix-ui/react-tooltip"
import React from "react"
import type { ToolbarButtonProps } from "../type/types"

export const ToolbarButton: React.FC<ToolbarButtonProps> = ({
  label,
  onClick,
  disabled,
  active,
  className = "",
  children,
}) => (
  <Tooltip.Root>
    <Tooltip.Trigger asChild>
      <ToolbarPrimitive.Button
        className={`toolbar-icon-btn ${className} ${
          disabled ? "disabled" : ""
        } ${active ? "active" : ""}`}
        onClick={(e) => {
          e.stopPropagation()
          if (!disabled && onClick) onClick()
        }}
        disabled={disabled}
        data-state={active ? "on" : "off"}
        type="button"
      >
        {children}
      </ToolbarPrimitive.Button>
    </Tooltip.Trigger>
    <Tooltip.Content className="tooltip-content" side="bottom" align="center">
      {label}
    </Tooltip.Content>
  </Tooltip.Root>
)
