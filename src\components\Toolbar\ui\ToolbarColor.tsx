import React from "react"
import { ColorPicker } from "../../common/ColorPicker"
import type { ToolbarColorProps } from "../type/types"

export const ToolbarColor: React.FC<ToolbarColorProps> = ({
  label,
  value,
  onChange,
  disabled,
}) => (
  <div
    className={`toolbar-color-picker ${disabled ? "disabled" : ""}`}
    title={label}
    onClick={(e) => e.stopPropagation()}
  >
    <ColorPicker value={value} onChange={onChange} disabled={disabled} />
  </div>
)
