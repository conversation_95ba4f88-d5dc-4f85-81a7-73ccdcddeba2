/* eslint-disable @typescript-eslint/no-explicit-any */
import * as ToolbarPrimitive from "@radix-ui/react-toolbar"
import React from "react"
import { TooltipButton } from "../../common/TooltipButton"
import { ToolbarSelect } from "../ui/ToolbarSelect"

// 通用工具栏项目接口
export interface GenericToolbarItem {
  type: "button" | "select" | "color" | "separator" | "group" | "custom"
  id?: string
  label?: string
  icon?: React.ComponentType<any>
  text?: string
  className?: string
  onClick?: string | (() => void) // 支持字符串表达式或直接函数
  active?: string | boolean // 支持字符串表达式或直接布尔值
  disabled?: string | boolean // 支持字符串表达式或直接布尔值
  options?: string[] // 用于 select 类型
  value?: string | (() => any) // 支持字符串表达式或直接值
  onChange?: string | ((value: any) => void) // 支持字符串表达式或直接函数
  children?: GenericToolbarItem[] // 用于 group 类型
  customComponent?: React.ComponentType<any> // 用于 custom 类型
  customProps?: Record<string, any> // 传递给自定义组件的属性
}

// 通用工具栏渲染器属性
export interface GenericToolbarRendererProps {
  config: GenericToolbarItem[]
  context?: Record<string, any> // 用于表达式评估的上下文
  ariaLabel: string
  separatorClassName?: string
  className?: string
}

// 工具函数：安全地评估字符串表达式
const safeEval = (expression: string, context: Record<string, any>): any => {
  try {
    // 移除 TypeScript 类型断言语法，因为在运行时不支持
    const cleanExpression = expression.replace(/\s+as\s+\w+/g, "")
    // 创建一个函数来安全地评估表达式
    const func = new Function(
      ...Object.keys(context),
      `return ${cleanExpression}`
    )
    return func(...Object.values(context))
  } catch (error) {
    console.warn(`Failed to evaluate expression: ${expression}`, error)
    return false
  }
}

// 工具函数：获取方法引用
const getMethodRef = (
  methodName: string,
  context: Record<string, any>
): any => {
  // 支持嵌套属性访问，如 "props.onSomething"
  const keys = methodName.split(".")
  let result = context

  for (const key of keys) {
    result = result?.[key]
    if (result === undefined) break
  }

  return typeof result === "function" ? result : undefined
}

// 工具函数：解析值
const resolveValue = (
  value: string | boolean | (() => any) | undefined,
  context: Record<string, any>
): any => {
  if (typeof value === "string") {
    return safeEval(value, context)
  }
  if (typeof value === "function") {
    return value()
  }
  return value
}

// 工具函数：解析方法
const resolveMethod = (
  method: string | ((value?: any) => void) | undefined,
  context: Record<string, any>
): ((value?: any) => void) | undefined => {
  if (typeof method === "string") {
    return getMethodRef(method, context)
  }
  return method
}

// 分隔符组件
const ToolbarSeparator: React.FC<{ className?: string }> = ({ className }) => (
  <ToolbarPrimitive.Separator className={className} />
)

export const GenericToolbarRenderer: React.FC<GenericToolbarRendererProps> = ({
  config,
  context = {},
  ariaLabel,
  separatorClassName = "toolbar-separator",
  className = "",
}) => {
  const renderItem = (
    item: GenericToolbarItem,
    index: number
  ): React.ReactNode => {
    const isDisabled = resolveValue(item.disabled, context)
    const isActive = resolveValue(item.active, context)
    const currentValue = resolveValue(item.value, context)

    switch (item.type) {
      case "separator":
        return (
          <ToolbarSeparator
            key={`separator-${index}`}
            className={separatorClassName}
          />
        )

      case "button":
        return (
          <TooltipButton
            key={item.id || index}
            icon={item.icon}
            text={item.text}
            label={item.label}
            className={item.className}
            disabled={isDisabled}
            active={isActive}
            onClick={resolveMethod(item.onClick, context)}
          />
        )

      case "select":
        return (
          <ToolbarSelect
            key={item.id || index}
            label={item.label}
            options={item.options || []}
            value={currentValue}
            onChange={resolveMethod(item.onChange, context)}
            disabled={isDisabled}
            className={item.className}
          />
        )

      case "color":
        return (
          <ColorPicker
            key={item.id || index}
            label={item.label}
            value={currentValue}
            onChange={resolveMethod(item.onChange, context)}
            disabled={isDisabled}
            className={item.className}
          />
        )

      case "group":
        return (
          <ToolbarPrimitive.ToggleGroup
            key={item.id || index}
            type="single"
            className={item.className}
          >
            {item.children?.map((child, childIndex) =>
              renderItem(child, childIndex)
            )}
          </ToolbarPrimitive.ToggleGroup>
        )

      case "custom":
        if (item.customComponent) {
          const CustomComponent = item.customComponent
          return (
            <CustomComponent
              key={item.id || index}
              {...item.customProps}
              disabled={isDisabled}
              active={isActive}
              value={currentValue}
              context={context}
            />
          )
        }
        return null

      default:
        console.warn(`Unknown toolbar item type: ${item.type}`)
        return null
    }
  }

  return (
    <ToolbarPrimitive.Root className={className} aria-label={ariaLabel}>
      {config.map((item, index) => renderItem(item, index))}
    </ToolbarPrimitive.Root>
  )
}
