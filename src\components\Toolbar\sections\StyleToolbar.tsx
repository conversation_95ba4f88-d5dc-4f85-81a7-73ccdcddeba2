import React from "react"
import { styleToolbarConfig } from "../config/styleToolbarConfig"
import type { ToolbarSectionProps } from "../type/types"
import { ToolbarRenderer } from "../utils/ToolbarRenderer"
import "./index.css"

export const StyleToolbar: React.FC<ToolbarSectionProps> = (props) => {
  return (
    <ToolbarRenderer
      {...props}
      config={styleToolbarConfig}
      ariaLabel="样式工具栏"
      separatorClassName="toolbar-separator-vertical"
    />
  )
}
