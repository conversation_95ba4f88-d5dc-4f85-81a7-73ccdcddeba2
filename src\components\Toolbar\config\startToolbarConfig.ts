import React from "react"
import {
  AlignCenterIcon,
  AlignLeftIcon,
  AlignRightIcon,
  BoldIcon,
  ClearformatIcon,
  HyperlinkIcon,
  ItalicIcon,
  PackupIcon,
  PictureIcon,
  RedoIcon,
  RootThemeIcon,
  SameLevelIcon,
  SearchIcon,
  StyleBackgroundIcon,
  StyleBorderIcon,
  StyleCanvasIcon,
  StyleStructureIcon,
  SubThemeIcon,
  SummaryIcon,
  UndoIcon,
  UnfoldIcon,
  WatermarkIcon,
} from "../icons"

// 工具栏项目类型定义
export interface ToolbarItem {
  type: "button" | "select" | "color" | "separator" | "group"
  id?: string
  label?: string
  icon?: React.ComponentType
  text?: string
  className?: string
  onClick?: string // 对应 props 中的方法名
  active?: string // 对应 props 中的状态属性
  disabled?: boolean | string // 可以是布尔值或者条件表达式
  options?: string[] // 用于 select 类型
  value?: string // 对应 props 中的值属性
  onChange?: string // 对应 props 中的方法名
  children?: ToolbarItem[] // 用于 group 类型
}

// 开始工具栏配置
export const startToolbarConfig: ToolbarItem[] = [
  // 查找替换组
  {
    type: "button",
    id: "search",
    label: "查找替换",
    icon: SearchIcon,
    text: "查找替换",
    className: "toolbar-text-btn",
    disabled: "!isEnabled",
  },

  { type: "separator" },

  // 撤销重做组
  {
    type: "button",
    id: "undo",
    label: "撤销",
    icon: UndoIcon,
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "redo",
    label: "恢复",
    icon: RedoIcon,
    disabled: "!isEnabled",
  },

  { type: "separator" },

  // 清除格式
  {
    type: "button",
    id: "clearFormat",
    label: "清除格式",
    icon: ClearformatIcon,
    disabled: "!isEnabled",
  },

  { type: "separator" },

  // 字体选择组
  {
    type: "select",
    id: "fontFamily",
    label: "字体",
    options: ["微软雅黑", "宋体", "Arial", "Times New Roman"],
    value: "selectedNodeStyle?.fontFamily || '微软雅黑'",
    onChange: "onFontFamilyChange",
    disabled: "!isEnabled",
    className: "toolbar-font-select",
  },
  {
    type: "select",
    id: "fontSize",
    label: "字号",
    options: ["12", "14", "16", "18", "20", "24", "28", "32"],
    value: "(selectedNodeStyle?.fontSize?.toString()) || '14'",
    onChange: "onFontSizeChange",
    disabled: "!isEnabled",
    className: "toolbar-size-select",
  },

  { type: "separator" },

  // 文本格式组
  {
    type: "button",
    id: "bold",
    label: "加粗",
    icon: BoldIcon,
    onClick: "onToggleBold",
    active: "selectedNodeStyle?.fontWeight === 'bold'",
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "italic",
    label: "斜体",
    icon: ItalicIcon,
    onClick: "onToggleItalic",
    active: "selectedNodeStyle?.fontStyle === 'italic'",
    disabled: "!isEnabled",
  },
  {
    type: "color",
    id: "fontColor",
    label: "字体颜色",
    value: "selectedNodeStyle?.color || '#000000'",
    onChange: "onColorChange",
    disabled: "!isEnabled",
  },

  { type: "separator" },

  // 对齐组
  {
    type: "button",
    id: "alignLeft",
    label: "左对齐",
    icon: AlignLeftIcon,
    onClick: "() => onTextAlignChange && onTextAlignChange('left')",
    active: "selectedNodeStyle?.textAlign === 'left'",
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "alignCenter",
    label: "居中对齐",
    icon: AlignCenterIcon,
    onClick: "() => onTextAlignChange && onTextAlignChange('center')",
    active: "selectedNodeStyle?.textAlign === 'center'",
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "alignRight",
    label: "右对齐",
    icon: AlignRightIcon,
    onClick: "() => onTextAlignChange && onTextAlignChange('right')",
    active: "selectedNodeStyle?.textAlign === 'right'",
    disabled: "!isEnabled",
  },

  { type: "separator" },

  // 主题操作组
  {
    type: "button",
    id: "addChild",
    label: "子主题",
    icon: SubThemeIcon,
    onClick: "onAddChildNode",
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "addSibling",
    label: "同级主题",
    icon: SameLevelIcon,
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "addParent",
    label: "父主题",
    icon: RootThemeIcon,
    disabled: "!isEnabled",
  },

  { type: "separator" },

  // 插入元素组
  {
    type: "button",
    id: "summary",
    label: "概要",
    icon: SummaryIcon,
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "border",
    label: "外框",
    icon: StyleBorderIcon,
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "picture",
    label: "图片",
    icon: PictureIcon,
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "hyperlink",
    label: "超链接",
    icon: HyperlinkIcon,
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "watermark",
    label: "水印",
    icon: WatermarkIcon,
    disabled: "!isEnabled",
  },

  { type: "separator" },

  // 样式组
  {
    type: "button",
    id: "canvas",
    label: "画布",
    icon: StyleCanvasIcon,
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "background",
    label: "风格",
    icon: StyleBackgroundIcon,
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "structure",
    label: "结构",
    icon: StyleStructureIcon,
    disabled: "!isEnabled",
  },

  { type: "separator" },

  // 展开收起组
  {
    type: "button",
    id: "collapse",
    label: "收起",
    icon: PackupIcon,
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "expand",
    label: "展开",
    icon: UnfoldIcon,
    disabled: "!isEnabled",
  },
]
