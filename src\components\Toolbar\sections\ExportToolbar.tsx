import * as ToolbarPrimitive from "@radix-ui/react-toolbar"
import React from "react"
import { TooltipButton } from "../../common/TooltipButton"
export const ExportToolbar: React.FC = () => {
  return (
    <ToolbarPrimitive.Root className="toolbar" aria-label="导出工具栏">
      <TooltipButton label="导出为图片">图片</TooltipButton>
      <TooltipButton label="导出为PDF">PDF</TooltipButton>
      <TooltipButton label="导出大纲">大纲</TooltipButton>
      <TooltipButton label="导出为POS">POS</TooltipButton>
      <TooltipButton label="导出为Excel">Excel</TooltipButton>
      <TooltipButton label="导出为FreeMind">FreeMind</TooltipButton>
      <TooltipButton label="PPT大纲文档">PPT大纲文档</TooltipButton>
      <TooltipButton label="全部制作POS文件">全部制作POS文件</TooltipButton>
    </ToolbarPrimitive.Root>
  )
}
