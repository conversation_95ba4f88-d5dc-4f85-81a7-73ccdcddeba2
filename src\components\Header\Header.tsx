import * as Tooltip from "@radix-ui/react-tooltip"
import React from "react"
import "./Header.css"
import { FileControls } from "./sections/FileControls"
import { TabNavigation } from "./sections/TabNavigation"
import { UserControls } from "./sections/UserControls"
import type { HeaderProps } from "./type/types"

export const Header: React.FC<HeaderProps> = ({ activeTab, onTabChange }) => {
  return (
    <Tooltip.Provider delayDuration={100}>
      <header className="header">
        <div className="header-container">
          <FileControls />
          <TabNavigation activeTab={activeTab} onTabChange={onTabChange} />
          <UserControls />
        </div>
      </header>
    </Tooltip.Provider>
  )
}
