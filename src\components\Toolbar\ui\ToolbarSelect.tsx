import React from "react"
import type { ToolbarSelectProps } from "../type/types"

export const ToolbarSelect: React.FC<ToolbarSelectProps> = ({
  label,
  value,
  options,
  onChange,
  disabled,
  className,
}) => (
  <select
    className={className || "toolbar-font-select"}
    value={value}
    onChange={(e) => {
      e.stopPropagation()
      onChange(e.target.value)
    }}
    disabled={disabled}
    title={label}
    onClick={(e) => e.stopPropagation()}
  >
    {options.map((opt) => (
      <option key={opt} value={opt}>
        {opt}
      </option>
    ))}
  </select>
)
