export interface ToolbarButtonProps {
  label: string
  onClick?: () => void
  disabled?: boolean
  active?: boolean
  className?: string
  children: React.ReactNode
}

export interface ToolbarSelectProps {
  label: string
  value: string | number
  options: string[]
  onChange: (value: string) => void
  disabled?: boolean
  className?: string
}

export interface ToolbarColorProps {
  label: string
  value: string
  onChange: (color: string) => void
  disabled?: boolean
}

export interface ToolbarSeparatorProps {
  className?: string
}

export interface NodeStyle {
  fontFamily?: string
  fontSize?: number
  fontWeight?: string
  fontStyle?: string
  color?: string
  textAlign?: "left" | "center" | "right"
  borderWidth?: number
}

export interface ToolbarProps {
  activeTab: string
  selectedNodeId: string | null
  isEditingNode?: string | null
  selectedNodeStyle?: NodeStyle
  onAddChildNode?: () => void
  onToggleBold?: () => void
  onToggleItalic?: () => void
  onColorChange?: (color: string) => void
  onFontFamilyChange?: (fontFamily: string) => void
  onFontSizeChange?: (fontSize: number) => void
  onTextAlignChange?: (align: "left" | "center" | "right") => void
  onBorderWidthChange?: (borderWidth: number) => void
}

export interface ToolbarSectionProps {
  selectedNodeId: string | null
  selectedNodeStyle?: NodeStyle
  onAddChildNode?: () => void
  onToggleBold?: () => void
  onToggleItalic?: () => void
  onColorChange?: (color: string) => void
  onFontFamilyChange?: (fontFamily: string) => void
  onFontSizeChange?: (fontSize: number) => void
  onTextAlignChange?: (align: "left" | "center" | "right") => void
  onBorderWidthChange?: (borderWidth: number) => void
}
