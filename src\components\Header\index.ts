// 主组件
export { Header } from "./Header"

// 子组件
export { FileControls } from "./sections/FileControls"
export { TabNavigation } from "./sections/TabNavigation"
export { UserControls } from "./sections/UserControls"

// 常量和工厂函数
export {
  createFileActions,
  createFileOperations,
  createLocationOperations,
  tabConfig
} from "./config/constants"

// Hooks
export { useFileActions, useFileState, useUserActions } from "./hooks"

// 类型
export type {
  ActionItem,
  FileControlsProps,
  FileInfo,
  HeaderProps,
  IconActionItem,
  TabItem,
  TabNavigationProps,
  UserControlsProps
} from "./type/types"

// 工具函数
export { getIconComponent } from "./icon/iconUtils"

