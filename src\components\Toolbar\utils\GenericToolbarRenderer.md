# GenericToolbarRenderer - 通用工具栏渲染器

## 概述

`GenericToolbarRenderer` 是一个完全通用的工具栏渲染组件，可以通过配置文件驱动任何类型的工具栏。它支持多种组件类型、动态属性、表达式评估和自定义组件。

## 特性

### ✅ 支持的组件类型
- **button** - 按钮组件
- **select** - 下拉选择组件  
- **color** - 颜色选择组件
- **separator** - 分隔符
- **group** - 组合组件
- **custom** - 自定义组件

### ✅ 动态属性支持
- **表达式字符串**：如 `"selectedNodeId !== null"`
- **直接值**：如 `true`, `false`
- **函数**：如 `() => someValue()`

### ✅ 灵活的事件处理
- **方法名字符串**：如 `"onSomething"`
- **嵌套方法**：如 `"props.onSomething"`
- **直接函数**：如 `(value) => console.log(value)`

## 使用方法

### 基本用法

```typescript
import { GenericToolbarRenderer } from './utils/GenericToolbarRenderer'

const MyToolbar = () => {
  const context = {
    isEnabled: true,
    selectedItem: null,
    onSave: () => console.log('保存'),
    onDelete: () => console.log('删除'),
  }

  const config = [
    {
      type: "button",
      id: "save",
      label: "保存",
      icon: SaveIcon,
      onClick: "onSave",
      disabled: "!isEnabled",
    },
    {
      type: "separator"
    },
    {
      type: "button", 
      id: "delete",
      label: "删除",
      icon: DeleteIcon,
      onClick: "onDelete",
      disabled: "selectedItem === null",
    }
  ]

  return (
    <GenericToolbarRenderer
      config={config}
      context={context}
      ariaLabel="我的工具栏"
    />
  )
}
```

### 高级用法

```typescript
const AdvancedToolbar = () => {
  const [fontSize, setFontSize] = useState('14')
  const [color, setColor] = useState('#000000')

  const context = {
    fontSize,
    color,
    onFontSizeChange: setFontSize,
    onColorChange: setColor,
    isTextSelected: true,
  }

  const config = [
    {
      type: "select",
      id: "fontSize",
      label: "字号",
      options: ["12", "14", "16", "18", "20"],
      value: "fontSize",
      onChange: "onFontSizeChange",
      disabled: "!isTextSelected",
    },
    {
      type: "color",
      id: "textColor", 
      label: "文字颜色",
      value: "color",
      onChange: "onColorChange",
    },
    {
      type: "custom",
      id: "customWidget",
      customComponent: MyCustomComponent,
      customProps: {
        title: "自定义组件",
        data: [1, 2, 3]
      }
    }
  ]

  return (
    <GenericToolbarRenderer
      config={config}
      context={context}
      ariaLabel="高级工具栏"
      separatorClassName="my-separator"
    />
  )
}
```

## 配置项详解

### GenericToolbarItem 接口

```typescript
interface GenericToolbarItem {
  type: "button" | "select" | "color" | "separator" | "group" | "custom"
  id?: string                           // 唯一标识符
  label?: string                        // 显示标签
  icon?: React.ComponentType<any>       // 图标组件
  text?: string                         // 按钮文本
  className?: string                    // CSS 类名
  onClick?: string | (() => void)       // 点击事件
  active?: string | boolean             // 激活状态
  disabled?: string | boolean           // 禁用状态
  options?: string[]                    // 选项列表（select 类型）
  value?: string | (() => any)          // 当前值
  onChange?: string | ((value: any) => void) // 变更事件
  children?: GenericToolbarItem[]       // 子项目（group 类型）
  customComponent?: React.ComponentType<any> // 自定义组件
  customProps?: Record<string, any>     // 自定义属性
}
```

### 表达式语法

支持 JavaScript 表达式，可以访问 context 中的所有属性：

```typescript
// 简单条件
"isEnabled"
"!isDisabled" 
"selectedItem !== null"

// 复杂表达式
"selectedItems.length > 0"
"user.role === 'admin'"
"(fontSize || '14')"

// 方法调用
"getValue()"
"props.getSomething()"
```

## 扩展性

### 添加新的组件类型

1. 在 `GenericToolbarItem` 接口中添加新的 type
2. 在 `renderItem` 方法中添加对应的 case
3. 创建对应的组件

### 自定义组件

```typescript
const MyCustomComponent = ({ value, onChange, context, ...props }) => {
  return (
    <div>
      <input 
        value={value} 
        onChange={(e) => onChange?.(e.target.value)}
        {...props}
      />
    </div>
  )
}

// 在配置中使用
{
  type: "custom",
  customComponent: MyCustomComponent,
  customProps: { placeholder: "输入内容" }
}
```

## 与现有工具栏的兼容性

现有的 `StartToolbar` 和 `StyleToolbar` 可以轻松迁移到 `GenericToolbarRenderer`：

```typescript
// 旧版本
<ToolbarRenderer {...props} config={startToolbarConfig} />

// 新版本  
<GenericToolbarRenderer 
  config={startToolbarConfig} 
  context={props}
  ariaLabel="开始工具栏" 
/>
```

## 最佳实践

1. **配置文件分离**：将配置放在单独的文件中
2. **类型安全**：为配置创建 TypeScript 类型
3. **性能优化**：使用 `useMemo` 缓存配置
4. **错误处理**：在表达式中添加适当的错误处理
5. **测试**：为每个配置项编写单元测试
