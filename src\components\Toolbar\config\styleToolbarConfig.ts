import {
  StyleBackgroundIcon,
  StyleBorderIcon,
  StyleCanvasIcon,
  StyleClearIcon,
  StyleLineColorIcon,
  StyleLineIcon,
  StyleLineWidthIcon,
  StyleNodeIcon,
  StyleNodeStyleIcon,
  StyleShapeIcon,
  StyleStructureIcon,
} from "../icons"
import { StyleBorderColorIcon } from "../icons/styleToolbar/StyleBorderColor"
import type { ToolbarItem } from "./startToolbarConfig"

// 样式工具栏配置
export const styleToolbarConfig: ToolbarItem[] = [
  // 节点样式组
  {
    type: "button",
    id: "nodeStyle",
    label: "节点样式",
    icon: StyleNodeIcon,
    text: "节点样式",
    className: "toolbar-text-btn",
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "shape",
    label: "形状",
    icon: StyleShapeIcon,
    text: "形状",
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "nodeBackground",
    label: "节点背景",
    icon: StyleBackgroundIcon,
    text: "节点背景",
    disabled: "!isEnabled",
  },

  { type: "separator" },

  // 连线样式组
  {
    type: "button",
    id: "lineType",
    label: "连线类型",
    icon: StyleLineIcon,
    text: "连线类型",
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "lineColor",
    label: "连线颜色",
    icon: StyleLineColorIcon,
    text: "连线颜色",
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "lineWidth",
    label: "连线宽度",
    icon: StyleLineWidthIcon,
    text: "连线宽度",
    disabled: "!isEnabled",
  },

  { type: "separator" },

  // 边框样式组
  {
    type: "group",
    id: "borderWidthGroup",
    className: "toolbar-border-width-group",
    children: [
      {
        type: "button",
        id: "borderWidthLabel",
        label: "边框宽度",
        text: "边框宽度",
        disabled: "!isEnabled",
      },
      {
        type: "select",
        id: "borderWidth",
        label: "边框宽度",
        options: ["0", "1", "2", "3", "4", "5"],
        value: "(selectedNodeStyle?.borderWidth?.toString()) || '1'",
        onChange: "onBorderWidthChange",
        disabled: "!isEnabled",
        className: "toolbar-border-width-select",
      },
    ],
  },
  {
    type: "button",
    id: "borderColor",
    label: "边框颜色",
    icon: StyleBorderColorIcon,
    text: " 边框颜色",
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "borderType",
    label: "边框类型",
    icon: StyleBorderIcon,
    text: "边框类型",
    disabled: "!isEnabled",
  },

  { type: "separator" },

  // 清除样式
  {
    type: "button",
    id: "clearStyle",
    label: "清除样式",
    icon: StyleClearIcon,
    text: "清除样式",
    disabled: "!isEnabled",
  },

  { type: "separator" },

  // 全局样式组
  {
    type: "button",
    id: "canvas",
    label: "画布",
    icon: StyleCanvasIcon,
    text: "画布",
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "globalStyle",
    label: "风格",
    icon: StyleNodeStyleIcon,
    text: "风格",
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "structure",
    label: "结构",
    icon: StyleStructureIcon,
    text: "结构",
    disabled: "!isEnabled",
  },
]
